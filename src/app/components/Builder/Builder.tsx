import React from "react";
import { useGetStats } from "../../services/stats.hook";
import VideoPlayer from "../VideoPlayer/VideoPlayer";
import { AppButton } from "../app-button";
import Link from "next/link";

export default function Builder() {
  const { data: stats } = useGetStats();
  return (
    <div className="flex flex-col items-center rounded-lg border border-[#D0BCFF] p-4 lg:p-6">
      <div className="grid size-full grid-cols-3 gap-2 border-b-2 border-[#D0BCFF] text-center lg:gap-10">
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            FREE
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            For Open Source Projects
          </h4>
        </div>
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            {stats?.abiDataCount ? stats?.abiDataCount : "90"}
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Repos built
          </h4>
        </div>
        <div className="w-auto">
          <h3 className="sub-title-custom font-bold tracking-normal sm:text-[64px] md:text-[72px] lg:text-[100px]">
            {stats?.abiJobCount
              ? parseInt(String(stats?.abiJobCount)) * 2
              : "200"}
          </h3>
          <h4 className="mb-5 font-thin leading-[16px] tracking-normal text-white lg:mb-[32px] lg:text-[32px] lg:leading-[33px]">
            Hours saved
          </h4>
        </div>
      </div>
      <div className="mt-10 flex w-full flex-col">
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">
            It's never been easier
          </h3>
          <p className="font-thin text-white lg:text-[24px]">
            2 click scaffolding a State of the Art Medusa, Echidna, Hamos and
            Kontrol Invariant Testing Setup
          </p>
        </div>
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">
            use the right tools for the job
          </h3>
          <p className="font-thin text-white lg:text-[24px]">
            Use Foundry to develop and debug, use medusa and echidna for
            invariant testing
          </p>
        </div>
        <div className="mb-2 rounded-lg bg-[#171225] p-5 lg:mb-4">
          <h3 className="uppercase text-white lg:text-[40px]">
            Only as opinionated as necessary
          </h3>
          <p className="font-thin text-white lg:text-[24px]">
            Compatible with any Foundry project. Zero configuration necessary
          </p>
        </div>
      </div>
      <div className="mt-3 w-full lg:mt-10 lg:w-3/5">
        <div className="order-1 flex w-full flex-col items-center justify-center text-left lg:order-2">
          <h3 className="font-bold uppercase text-[#D0BCFF] lg:text-[24px]">
            check how it works
          </h3>
          <VideoPlayer link={"https://www.youtube.com/embed/8FRmFlmhvfY"} />
        </div>
      </div>
      <Link
        href="/tools/sandbox"
        className="m-0 flex flex-row items-center justify-center p-0"
        target="_blank"
        rel="noopener noreferrer"
      >
        <AppButton variant="secondary" className="m-0 p-0">
          Use the Free Builder
        </AppButton>
      </Link>
    </div>
  );
}
