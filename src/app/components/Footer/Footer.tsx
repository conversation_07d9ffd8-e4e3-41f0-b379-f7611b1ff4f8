import { useGetDiscord } from "@/app/services/discord.hook.ts";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ReconLogoIcon } from "../recon-logo-icon";

export default function Footer() {
  const { data: discordUrl } = useGetDiscord();

  return (
    <div className="flex w-full flex-col  bg-[#65558F14]">
      <div className="flex size-full flex-col justify-center gap-5 p-[24px] text-white md:grid-cols-2  lg:grid lg:grid-cols-4 lg:p-[40px]">
        <div className="flex flex-col">
          <h2 className="text-[24px] uppercase ">Connect with us</h2>
          <Link
            href="https://x.com/getreconxyz"
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
          >
            X || Twitter
          </Link>
          <Link
            href="https://github.com/Recon-Fuzz"
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
          >
            Github
          </Link>
          {discordUrl && (
            <Link
              href={discordUrl}
              className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            >
              Discord
            </Link>
          )}
          <Link
            href="https://getrecon.substack.com/"
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
          >
            Substack
          </Link>
        </div>
        <div className="flex flex-col">
          <h2 className="text-[24px] uppercase ">Developers</h2>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px] text-white"
            href="/dashboard"
          >
            Login
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px] text-white"
            href="/dynamic-replacement-demo"
          >
            Dynamic Replacement Demo
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px] text-white"
            href="https://t.me/gallodasballo"
          >
            Talk to Sales
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px] text-white"
            href="https://docs.google.com/presentation/d/1XX48swlia6O_Paqsvv3Tt466d1Lgh0zggSOqy8TPZcM/edit?usp=sharing"
          >
            Live Exploit Prevention (Stateful Monitoring)
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px] text-white"
            href="https://book.getrecon.xyz/"
          >
            Docs
          </Link>
        </div>
        <div className="flex flex-col">
          <h2 className="text-[24px] uppercase ">Tools</h2>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            href="/tools/medusa"
          >
            Medusa Logs Scraper
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            href="/tools/echidna"
          >
            Echidna Logs Scraper
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            href="/tools/sandbox"
          >
            Invariant Test Builder
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            href="/tools/bytecode-compare"
          >
            Bytecode compare
          </Link>
          <Link
            className="mb-[13px] block text-[22px] font-thin leading-[26px]"
            href="/tools/bytecode-to-interface"
          >
            Bytecode to interface
          </Link>
        </div>
        <div className="mx-auto">
          <ReconLogoIcon />
        </div>
      </div>
      <div className="flex w-full flex-row items-center justify-between p-5 text-white">
        <Link className="block text-[12px] font-thin leading-[26px]" href="/">
          Recon
        </Link>
        <Link
          className="block text-[12px] font-thin leading-[26px]"
          href="/privacy"
        >
          Privacy Policy
        </Link>
        <Link
          className="block text-[12px] font-thin leading-[26px]"
          href="/terms"
        >
          Terms of Service
        </Link>
        <p className="text-[12px]">© 2024 Recon Fuzz. All rights reserved.</p>
        <Image
          src="/recon-logo.svg"
          alt="Recon Logo"
          width={70}
          height={60}
          priority
        />
      </div>
    </div>
  );
}
