import React from "react";
import VideoPlayer from "../VideoPlayer/VideoPlayer";

interface MediaProps {
  short?: boolean;
}

const VIDEOS = [
  "https://www.youtube.com/embed/8-qWL2Dcgpc",
  "https://www.youtube.com/embed/WDdFVZpTAZo",
  "https://www.youtube.com/embed/DHAvBrsITRU",
  "https://www.youtube.com/embed/XCEICO6uGrE",
  "https://www.youtube.com/embed/WJLU2OJO430",
  "https://www.youtube.com/embed/WasI2-DWceM",
  "https://www.youtube.com/embed/NxbB5jjQ_Mk",
  "https://www.youtube.com/embed/ZM6479HeI5U",
  "https://www.youtube.com/embed/JvGjQAeSBB4",
  "https://www.youtube.com/embed/AT3fMhPDZFU",
  "https://www.youtube.com/embed/3pvWq_zBauY",
  "https://www.youtube.com/embed/RWvA9myV_LQ",
  "https://www.youtube.com/embed/63BVVCpJk8U",
  "https://www.youtube.com/embed/AMCN1HP84BQ",
  "https://www.youtube.com/embed/kEC3UVyR5-Q",
  "https://www.youtube.com/embed/fXG2JwvoFZ0",
  "https://www.youtube.com/embed/-ghFy21pcNo",
];

export default function Media({ short }: MediaProps) {
  const displayedVideos = short ? VIDEOS.slice(0, 3) : VIDEOS;
  return (
    <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
      {displayedVideos.map((entry) => (
        <VideoPlayer
          key={entry}
          link={entry}
          forcedHeight={400}
          forcedWidth={400}
        />
      ))}
    </div>
  );
}
