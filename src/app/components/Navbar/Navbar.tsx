"use client";
import { useState } from "react";
import Image from "next/image";
import { AppButton } from "../app-button";
import Link from "next/link";
import { FaTelegram } from "react-icons/fa";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  return (
    <div className="fixed z-[1000] w-full">
      <div className="flex h-20 w-full items-center justify-between bg-[#E6E0E91F] px-5 py-4 backdrop-blur-md">
        <div className="max-w-[20%]">
          <Link href="/" passHref className="inline list-none px-2.5">
            <Image
              src="/recon-logo.svg"
              alt="Recon Logo"
              width={70}
              height={60}
              priority
            />
          </Link>
        </div>
        <div className="hidden max-w-[80%] flex-row items-center justify-between lg:flex">
          <div className="flex flex-row justify-between px-5 text-white">
            <Link
              href="/#benefits"
              passHref
              className="inline list-none px-2.5"
            >
              Why
            </Link>
            <Link
              href="/#testimonials"
              passHref
              className="inline list-none px-2.5"
            >
              Testimonials
            </Link>
            <Link
              href="/#trophies"
              passHref
              className="inline list-none px-2.5"
            >
              Trophies
            </Link>
            <Link href="/#team" passHref className="inline list-none px-2.5">
              Team
            </Link>
            <Link
              href="/#services"
              passHref
              className="inline list-none px-2.5"
            >
              Services
            </Link>
            <Link href="/#builder" passHref className="inline list-none px-2.5">
              Builder
            </Link>
            <Link href="/bootcamp" passHref className="inline list-none px-2.5">
              Bootcamp
            </Link>
            <Link
              href="https://book.getrecon.xyz/"
              passHref
              className="inline list-none px-2.5"
            >
              Docs
            </Link>
          </div>
          <div className="flex flex-row justify-between px-5 text-white">
            <Link
              href="https://t.me/GalloDaSballo"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="secondary" rightIcon={<FaTelegram />}>
                Questions? Ask the Founder
              </AppButton>
            </Link>
            <Link href="/dashboard" className="m-0 w-[150px] p-0 text-center">
              <AppButton variant="outline">Log in &gt;</AppButton>
            </Link>
          </div>
        </div>
        <div className="flex items-center lg:hidden">
          <button
            onClick={toggleMenu}
            className="text-white focus:outline-none"
          >
            {isMenuOpen ? (
              <svg
                className="size-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                ></path>
              </svg>
            ) : (
              <svg
                className="size-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16m-7 6h7"
                ></path>
              </svg>
            )}
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="fixed inset-0 top-20 z-[999] flex flex-col items-center justify-center bg-black bg-opacity-95 text-white">
          <ul className="flex w-full flex-col items-center space-y-8 text-lg">
            <Link
              href="/#benefits"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Why
            </Link>
            <Link
              href="/#testimonials"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Testimonials
            </Link>
            <Link
              href="/#trophies"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Trophies
            </Link>
            <Link
              href="/#team"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Team
            </Link>
            <Link
              href="/#services"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Services
            </Link>
            <Link
              href="/#builder"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Free Builder
            </Link>
            <Link
              href="/bootcamp"
              passHref
              className="sub-title-custom uppercase"
              onClick={closeMenu}
            >
              Bootcamp
            </Link>
          </ul>
          <div className="mt-8 flex w-full flex-col items-center space-y-4">
            <Link
              href="https://t.me/GalloDaSballo"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="secondary" rightIcon={<FaTelegram />}>
                Questions? Ask the Founder
              </AppButton>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default Navbar;
