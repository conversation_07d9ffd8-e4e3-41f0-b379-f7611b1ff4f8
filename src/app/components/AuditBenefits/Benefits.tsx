import React from "react";

const BenefitsArray = [
  {
    title: "THE MISSING PIECE",
    description:
      "Invariant Testing is often the missing piece to reduce the number of bugs protocols go to audit with",
    upcoming: false,
    experimental: false,
  },
  {
    title: "CODE THAT GROWS WITH YOU",
    description:
      "Invariant Tests help you specify your system behavior, helping increase it's predictability",
    upcoming: false,
    experimental: false,
  },
  {
    title: "NEVER MAKE THE SAME MISTAKE AGAIN",
    description:
      "Invariant Tests can run on every edit, meaning once you fix a bug, they'll check against it",
    upcoming: false,
    experimental: false,
  },
  {
    title: "WORLD CLASS MANUAL REVIEW",
    description:
      "Recon Audits are lead exclusively by seasoned veterans, we only take audits for which we have an edge",
    upcoming: false,
    experimental: false,
  },
  {
    title: "RECON PRO",
    description:
      "Our fully developed cloud platform saves you time with features nobody else has built. Recon Pro is included in every engagement",
    upcoming: false,
    experimental: false,
  },
  {
    title: "LIVE MONITORING",
    description:
      "Recon suites are easily reusable for live monitoring, these tests try to predict exploits instead of",
    upcoming: false,
    experimental: false,
  },
];

interface BenefitProps {
  title: string;
  description: string;
  upcoming?: boolean;
  experimental?: boolean;
}

function Benefit({ title, description, upcoming, experimental }: BenefitProps) {
  return (
    <div className="relative mx-auto flex h-auto w-full max-w-[448px] flex-col items-center justify-start overflow-hidden rounded-lg bg-[rgba(103,80,164,0.12)] p-5 pt-10 text-center">
      {upcoming && (
        <span className="absolute right-0 top-0 flex h-[36px] w-[176px] items-center justify-center rounded-bl-lg rounded-tr-lg bg-gradient-to-r from-[#EA5A4E] to-[#ED93D3] text-sm font-semibold text-white shadow-md">
          Upcoming Feature
        </span>
      )}
      <div className="flex size-full flex-col justify-between">
        <div className="flex w-full flex-col">
          <h2 className="w-full overflow-hidden break-words text-[24px] font-bold capitalize leading-none text-white md:text-[32px] lg:text-[52px]">
            {title}
          </h2>
        </div>
        <p className="mt-4 break-words text-[14px] text-white lg:text-[18px]">
          {description}
        </p>
        {experimental && (
          <p className="text-center text-[10px] font-light uppercase tracking-wider text-white lg:text-[14px]">
            Experimental
          </p>
        )}
      </div>
    </div>
  );
}

export default function Benefits() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <Benefit
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}
