import React from "react";
import { AppButton } from "../app-button";
import { CiCalendarDate } from "react-icons/ci";
import Link from "next/link";
import { FaTelegram } from "react-icons/fa";

export default function Service() {
  return (
    <div className="flex flex-col items-center rounded-lg border border-[#D0BCFF] p-3 lg:p-10">
      <h3 className="sub-title-custom text-center font-bold leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
        SERVICES
      </h3>
      <h4 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
        Boutique Audits, we can write code and break invariants, whether you
        want us to code with you perform manual review
      </h4>
      <Link
        href="https://t.me/GalloDaSballo"
        className="m-0 flex flex-row items-center justify-center p-0 text-center"
        target="_blank"
        rel="noopener noreferrer"
      >
        <AppButton
          variant="secondary"
          className="m-0 p-0"
          rightIcon={<FaTelegram />}
        >
          Questions? Ask the Founder
        </AppButton>
      </Link>
      <div className="mt-3 flex w-full flex-col items-start justify-center space-y-6 rounded-s bg-[#1E1E1E] p-3 text-left text-white lg:w-4/5 lg:p-5">
        <h3 className="text-[20px]">
          We can support your team at all stages of development
        </h3>
        <div>
          <h4 className="text-[20px] uppercase">Early Stage:</h4>
          <p>Define key invariants, scaffold and maintain invariant testers</p>
          <p>Add invariants as the project grows</p>
        </div>

        <div>
          <h4 className="text-[20px] uppercase">Pre Audit Stage:</h4>
          <p>
            Reach 100% Coverage, handout to your Auditors a full set of test
            repros to produce meaningful states or broken properties
          </p>
        </div>

        <div>
          <h4 className="text-[20px] uppercase">Solo Review Stage:</h4>
          <p>
            Recon is made by highly respected SRs that can help you with Manual
            Review
          </p>
        </div>

        <div>
          <h4 className="text-[20px] uppercase">Audit Stage:</h4>
          <p>
            We can support you during audits, by adding new properties flagged
            by your reviewers and by reproducing bugs in invariant tests as a
            means to ensure they are not introduced later
          </p>
          <p>
            Our cloud runners ensure you can quickly queue and test fixes, no
            more waiting for your engineer to come back from the weekend
          </p>
        </div>

        <div>
          <h4 className="text-[20px] uppercase">Our offers:</h4>
          <ul className="space-y-4">
            <li>
              <span className="text-[18px] font-bold">Manual Review</span> &gt;
              A high quality review done by top Security Researchers, ideally
              paired with Invariant Testing
            </li>
            <li>
              <span className="text-[18px] font-bold">
                Invariant Test Writing
              </span>{" "}
              &gt; Testing written by an experienced fuzzing engineer, for
              projects that want to skill up their codebase, includes unlimited
              cloud runs during the engagement
            </li>
            <li>
              <span className="text-[18px] font-bold">Recon Pro</span> &gt;
              Cloud Fuzzing as a service, a versatile and easy way to run
              invariant testing in the cloud
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
