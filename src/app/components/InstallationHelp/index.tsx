"use client";

import { AppBackButton } from "@/app/components/app-back-button";
import { AppCode } from "@/app/components/app-code";
import { AppButton } from "../app-button";
import Link from "next/link";
import { FaGithub } from "react-icons/fa";

export default function InstallationHelp() {
  return (
    <div className="min-h-screen grow overflow-y-auto bg-dashboardBG">
      <div className="pl-[45px] pr-[80px] pt-[45px]">
        <h3 className="mb-[19px] flex items-center gap-[10px] text-[28px] leading-[33px] text-textPrimary">
          <AppBackButton />
          Installation help
        </h3>
        <p className="mb-[17px]  text-[22px] leading-[26px] text-textPrimary">
          1. Install Chimera & Setup Helpers
        </p>
        <div className="mb-[17px]">
          <AppCode
            code="forge install Recon-Fuzz/chimera Recon-Fuzz/setup-helpers --no-commit"
            language="bash"
          />
        </div>

        <p className="mb-[8px] text-[22px] leading-[26px] text-textPrimary">
          2. Add Chimera & Recon Setup Helpers To Mappings
        </p>
        <p className="mb-[17px] text-[16px] leading-[19px] text-textSecondary">
          For example, change the remappings to
        </p>
        <div className="mb-[17px]">
          <AppCode
            code={`remappings = [
    'forge-std/=lib/forge-std/src/',
    '@chimera/=lib/chimera/src/',
    '@recon/=lib/setup-helpers/src/'
  ]`}
            language="toml"
          />
        </div>
        {[
          "3. Add all Recon Files To /test",
          "4. Move `medusa.json` and `echidna.yaml` to the project root",
          "5. Manually Fix the Constructors",
          "You're done!",
        ].map((text, index) => (
          <p
            key={index}
            className="mb-[17px] text-[22px] leading-[26px] text-textPrimary"
          >
            {text}
          </p>
        ))}

        <p className="mb-[8px]  text-[22px] leading-[26px] text-textPrimary">
          6. Run Medusa with:
        </p>
        <div className="mb-[17px]">
          <AppCode code={`medusa fuzz`} language="bash" />
        </div>
        <p className="mb-[8px]  text-[22px] leading-[26px] text-textPrimary">
          7. Run Echidna with:
        </p>
        <div className="mb-[17px]">
          <AppCode
            code={`echidna . --contract CryticTester --config echidna.yaml`}
            language="bash"
          />
        </div>
        <Link href="https://github.com/Recon-Fuzz/create-chimera-app">
          <p className="mb-[8px]  text-[22px] leading-[26px] text-textPrimary">
            BONUS: Use the Create-Chimera-App Template{" "}
            <AppButton leftIcon={<FaGithub />}>Github</AppButton>
          </p>
        </Link>
      </div>
    </div>
  );
}
