"use client";

import { ReactNode } from "react";
import { cn } from "../helpers/cn";

interface GradientWrapperProps {
  children?: ReactNode;
  className?: string;
  useMainBg?: boolean; // If true, uses main-bg.jpg, otherwise uses bg-recon.jpeg with gradient
}

export function GradientWrapper({
  children,
  className,
  useMainBg = false,
}: GradientWrapperProps) {
  const backgroundStyle = useMainBg
    ? {
        backgroundImage: `url('/main-bg.jpg')`,
        backgroundBlendMode: "color, normal" as const,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }
    : {
        backgroundImage: `linear-gradient(0deg, #5C25D2, #5C25D2), url('/bg-recon.jpeg')`,
        backgroundBlendMode: "color, normal" as const,
        backgroundSize: "cover",
        backgroundPosition: "center",
      };

  return (
    <div className={cn("size-full", className)} style={backgroundStyle}>
      {children}
    </div>
  );
}
