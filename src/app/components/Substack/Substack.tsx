import Caroussel from "../Caroussel/Caroussel";
import SubstackPost from "./SubstackPost/SubstackPost";

const subStackList = [
  {
    title: "First Day At Invariant School",
    description:
      "Featured in Week in Ethereum, a simple introduction to Invariant Testing",
    img: "https://substackcdn.com/image/fetch/f_auto,q_auto:good,fl_progressive:steep/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1b09c951-2277-4819-89cc-be594f28a9e0_3840x2160.png",
    link: "https://getrecon.substack.com/p/first-day-at-invariant-school",
  },
  {
    title: "Reusable Properties for ERC7540 Vaults",
    description:
      "How to implement properties developer by the Recon + Centrifuge teams for ERC7540 vaults",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2Fe25c2f2d-8151-436b-8a55-550ea8576e6b_1748x1330.png",
    link: "https://open.substack.com/pub/getrecon/p/reusable-properties-for-erc7540-vaults?utm_campaign=post&utm_medium=web",
  },
  {
    title: "Lessons Learned from Fuzzing Centrifuge Protocol part 2",
    description: "Separating signal from noise in broken properties",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F1257c81c-35a8-4884-80b1-f82ba90068fa_1121x376.jpeg",
    link: "https://open.substack.com/pub/getrecon/p/lessons-learned-from-fuzzing-centrifuge-059?utm_campaign=post&utm_medium=web",
  },
  {
    title: "Finding Real Vulnerabilities with the Renzo-Fuzzing repo",
    description:
      "Using the renzo-fuzzing  repo to reproduce vulnerabilities from the Renzo audit report from code4rena",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F300e34c9-5242-4eb0-96b8-f4ce2a3b582a_2102x1178.png",
    link: "https://open.substack.com/pub/getrecon/p/finding-real-vulnerabilities-with?utm_campaign=post&utm_medium=web",
  },
  {
    title: "Lessons From The Fuzzing Trenches",
    description:
      "Lessons learned from building a fuzzing suite for Renzo Protocol",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F300e34c9-5242-4eb0-96b8-f4ce2a3b582a_2102x1178.png",
    link: "https://open.substack.com/pub/getrecon/p/lessons-from-the-fuzzing-trenches?utm_campaign=post&utm_medium=web",
  },
  {
    title: "Integrating EigenLayer Into Your Test Suite",
    description:
      "Deploy the entire EigenLayer system + simulate slashing events in your test suite",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F300e34c9-5242-4eb0-96b8-f4ce2a3b582a_2102x1178.png",
    link: "https://open.substack.com/pub/getrecon/p/integrating-eigenlayer-into-your?utm_campaign=post&utm_medium=web",
  },
  {
    title: "eBTC Retrospective",
    description:
      "A reflection on lessons learned in our extended fuzzing of eBTC",
    img: "https://substackcdn.com/image/fetch/w_320,h_213,c_fill,f_auto,q_auto:good,fl_progressive:steep,g_auto/https%3A%2F%2Fsubstack-post-media.s3.amazonaws.com%2Fpublic%2Fimages%2F300e34c9-5242-4eb0-96b8-f4ce2a3b582a_2102x1178.png",
    link: "https://open.substack.com/pub/getrecon/p/ebtc-retrospective?utm_campaign=post&utm_medium=web",
  },
];

export default function SubstackList() {
  return (
    <Caroussel data={subStackList}>
      {subStackList.map((subStack, index) => (
        <SubstackPost
          key={index}
          title={subStack.title}
          description={subStack.description}
          img={subStack.img}
          link={subStack.link}
        />
      ))}
    </Caroussel>
  );
}
