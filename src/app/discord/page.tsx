"use client";

import React from "react";
import Navbar from "../components/Navbar/Navbar";
import { GradientWrapper } from "../components/gradient-wrapper";
import Link from "next/link";
import { AppButton } from "../components/app-button";
import { useGetDiscord } from "../services/discord.hook.ts";

export default function Discord() {
  const { data: discordUrl } = useGetDiscord();

  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              Recon Discord
            </h1>
            <h2 className="main-title-custom mb-5 text-center text-[32px] font-bold leading-[48px] tracking-normal">
              400 Registerd and Growing!
            </h2>
            {discordUrl ? (
              <Link href={discordUrl}>
                <AppButton>Join the discord</AppButton>
              </Link>
            ) : (
              <h2>No Discord link</h2>
            )}
          </section>
        </main>
      </div>
    </div>
  );
}
