"use client";

import React from "react";
import Navbar from "../components/Navbar/Navbar";
import { GradientWrapper } from "../components/gradient-wrapper";
import Link from "next/link";
import { AppButton } from "../components/app-button";

export default function FuzzFest() {
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              FuzzFest
            </h1>
            <h2 className="main-title-custom mb-5 text-center text-[32px] font-bold leading-[48px] tracking-normal">
              Over 250 Registered!
            </h2>

            <iframe
              width="560"
              height="315"
              src="https://www.youtube.com/embed/Cqmu-mhSLt8?si=dT1TFNZzPZhEtjaN"
              title=""
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            ></iframe>
            <p className="my-3 self-center text-[24px] font-thin leading-[24px] text-white">
              {`A 3.45 hours event with the "crème de la crème" of the fuzzing scene`}
            </p>
            <p className="my-3 self-center text-[24px] font-thin leading-[24px] text-white">
              December 16th, 2024
            </p>
            <p className="my-3 self-center text-[24px] font-thin leading-[24px] text-white">
              Current Schedule Outline:
            </p>
            <ol className="mb-5">
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                11.45 am UTC - Intro
              </li>
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                12 pm UTC - Dacian: Epic Fuzz Testing Workshop
              </li>
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                13 pm UTC - Josselin: Contributing to Medusa
              </li>
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                13:30 pm UTC - Alex: A glimpse into the future of Invariant
                Testing
              </li>
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                14 pm UTC Benjamin: Review of the UniV4 Invariant Testing Suite
              </li>
              <li className="self-center text-[18px] font-thin leading-[24px] text-white">
                14:45 pm UTC - Wrapup
              </li>
            </ol>
          </section>
        </main>
      </div>
    </div>
  );
}
