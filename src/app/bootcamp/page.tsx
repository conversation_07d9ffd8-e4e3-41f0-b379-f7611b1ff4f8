"use client";

import React from "react";
import Navbar from "../components/Navbar/Navbar";
import { GradientWrapper } from "../components/gradient-wrapper";
import Image from "next/image";
import Link from "next/link";
import { AppButton } from "../components/app-button";

export default function Bootcamp() {
  return (
    <div>
      <Navbar />
      <div className="main-container w-full overflow-x-hidden">
        <div className="absolute inset-0 z-0 bg-gradient-to-b from-transparent via-transparent to-black">
          <GradientWrapper />
        </div>
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              Invariant Testing Bootcamp at Home
            </h1>
            <p className="my-3 self-center text-[24px] font-thin leading-[24px] text-white">
              Learn invariant testing at your own pace, each video includes:
              Theory explanation, Coded Walkthrough and Coding Session
            </p>
            <p className="my-3 self-center text-[24px] font-thin leading-[24px] text-white">
              Join like-minded researchers, ask questions and go deeper in our
              Discord Community
              <div>
                <Link href="/discord">
                  <AppButton>Join Recon Discord</AppButton>
                </Link>
              </div>
            </p>

            <div>
              <Link
                href="https://x.com/i/broadcasts/1yoKMogdmLlJQ"
                target="_blank"
              >
                <Image
                  src="/bootcamp/chimera-1.png"
                  alt="Bootcamp 1"
                  width={800}
                  height={418}
                  className="mb-10"
                ></Image>
              </Link>
            </div>
            <div>
              <Link
                href="https://x.com/i/broadcasts/1mrGmPDlqgQKy"
                target="_blank"
              >
                <Image
                  src="/bootcamp/chimera-2.png"
                  alt="Bootcamp 2"
                  width={800}
                  height={418}
                  className="mb-10"
                ></Image>
              </Link>
            </div>
            <div>
              <Link
                href="https://x.com/i/broadcasts/1dRKZYvXNgvxB"
                target="_blank"
              >
                <Image
                  src="/bootcamp/chimera-3.png"
                  alt="Bootcamp 3"
                  width={800}
                  height={418}
                  className="mb-10"
                ></Image>
              </Link>
            </div>
            <div>
              <Link
                href="https://x.com/i/broadcasts/1BdxYqLoRgBxX"
                target="_blank"
              >
                <Image
                  src="/bootcamp/chimera-4.png"
                  alt="Bootcamp 4"
                  width={800}
                  height={418}
                  className="mb-10"
                ></Image>
              </Link>
            </div>
            <div>
              <Link
                href="https://x.com/i/broadcasts/1vOxwXWEpnqKB"
                target="_blank"
              >
                <Image
                  src="/bootcamp/chimera-5.png"
                  alt="Bootcamp 5"
                  width={800}
                  height={418}
                  className="mb-10"
                ></Image>
              </Link>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}
