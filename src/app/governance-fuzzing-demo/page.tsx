"use client";

import Link from "next/link";
import { AppLogo } from "../components/app-logo";
import { AppPageTitle } from "../components/app-page-title";
import Footer from "../components/Footer/Footer";
import { FormProvider, useForm } from "react-hook-form";
import { AppInput } from "../components/app-input";
import { AppCode } from "../components/app-code";
import { useMemo } from "react";

export type GitHubLinkFormValues = {
  // UX
  variableName: string;
  interface: string;
  value: string;
};
export default function GovernanceFuzzingDemo() {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<GitHubLinkFormValues>({
    defaultValues: {},
  });

  const variableName = watch("variableName");
  const value = watch("value");
  const interfaceType = watch("interface");

  // On this address

  // When this events fires -> EVENT_NAME / ABI

  // Do Dynamic Replacement
  // Each param of the event becomes itself a value that can be selected for Dynamic Replacement
  // Block choice (Use Lourens Toggle settings)

  // Arbitrary Calls
  // Temp Var Name
  // Address

  // VALUE can be
  // Static / Hardcoded Value
  // Value from Event (Event_PARAM_NAME)
  // Value from Call (Address + Calldata)

  const template = useMemo(() => {
    if (variableName && value && interfaceType) {
      return `
      {
        "prepareContracts": [
          {
            "target":"${variableName} = ${interfaceType}",
            "replacement":"${variableName} = ${interfaceType}(${value});",
            "endOfTargetMarker":"[^;]*",
            "targetContract":"Setup.sol"
          }
        ]
      }
      `;
    } else {
      return `{}`;
    }
  }, [variableName, value, interfaceType]);

  return (
    <div className="min-h-screen bg-dashboardBG">
      <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="p-[45px]">
        <AppPageTitle className="mb-[20px]">
          Governance Fuzzing Demo
        </AppPageTitle>
        <h3 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
          This is simple demo to explore the UX of Governance Fuzzing
        </h3>
        <FormProvider
          {...({
            register,
            handleSubmit,
            setValue,
            setError,
            watch,
            errors,
            isSubmitting,
          } as any)}
        ></FormProvider>
        <form onSubmit={() => {}}>
          <h3 className="mb-[22px] text-[28px] leading-[33px] text-textPrimary"></h3>

          <div className="flex flex-wrap gap-[40px]">
            <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
              <div className="min-w-[400px]">
                <AppInput
                  className="mb-[8px]"
                  label="Variable Name"
                  {...register("variableName")}
                  type="text"
                  defaultValue=""
                />
                <AppInput
                  className="mb-[8px]"
                  label="Interface"
                  {...register("interface")}
                  type="text"
                  defaultValue=""
                />
                <AppInput
                  className="mb-[8px]"
                  label="Value"
                  {...register("value")}
                  type="text"
                  defaultValue=""
                />
              </div>
            </div>
            <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
              <div className="min-w-[400px]">
                <div className="w-full">
                  <AppCode code={template} language="javascript" />
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <Footer />
    </div>
  );
}
