"use client";

import Link from "next/link";
import parser from "@solidity-parser/parser";
import { AppLogo } from "../components/app-logo";
import { AppPageTitle } from "../components/app-page-title";
import Footer from "../components/Footer/Footer";
import { FormProvider, useForm, useFieldArray } from "react-hook-form";
import { AppInput } from "../components/app-input";
import { AppCode } from "../components/app-code";
import { useEffect, useMemo, useState } from "react";
import { AppButton } from "../components/app-button";

export type FieldGroup = {
  variableName: string;
  value: string;
};

export type GitHubLinkFormValues = {
  fields: FieldGroup[];
};

interface Change {
  start: number;
  end: number;
  value: string;
}

const sampleCode = `// SPDX-License-Identifier: GPL-2.0
pragma solidity ^0.8.0;

import {BaseSetup} from "@chimera/BaseSetup.sol";

import {IInitiative} from "governance/interfaces/IInitiative.sol";

import {Initiative} from "src/Initiative.sol";
import {vm} from "@chimera/Hevm.sol";

abstract contract Setup is BaseSetup {
    Initiative initiative; // TODO IInitiative is not great, we should update it a bit

    // TODO: Update these values | OR use Dynamic Replacement
    address constant MAINNET_GOVERNANCE = address(0);
    address constant MAINNET_BOLD = address(0);

    uint256 constant MIN_GAS_TO_HOOK = 350_000;

    address constant
      NOT_FORMATTED=
        address(
        0x123);

    function setup() internal virtual override {
        initiative = new Initiative(MAINNET_GOVERNANCE, MAINNET_BOLD);

        // NOTE: Liquity could create a Interface and verify it via EIP-165
    }

    function setupFork() internal virtual {
        vm.warp(123);
        vm.roll(123);
        uint MIN_GAS_TO_HOOK = 123;
        // NOTE: Declared this way for Governance Fuzzing
        address INITIATIVE_TO_TEST = address(0x123123);
        initiative = Initiative(INITIATIVE_TO_TEST);
    }
}`;

export default function DynamicReplacementDemoNew() {
  const [template, setTemplate] = useState<string>("");
  const [sourceCode, setSourceCode] = useState<string>(sampleCode);

  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      fields: [
        { variableName: "MAINNET_GOVERNANCE", value: "0x1234567890" },
        { variableName: "MAINNET_BOLD", value: "0x0987654321" },
        { variableName: "MIN_GAS_TO_HOOK", value: "1e18" },
        { variableName: "NOT_FORMATTED", value: "address(0x111)" },
      ],
    },
  });

  const {
    register,
    control,
    watch,
    formState: { errors, validatingFields },
  } = methods;

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");
  const values = methods.getValues();
  useEffect(() => {
    try {
      const ast = parser.parse(sourceCode, { loc: true, range: true });
      const changes: Change[] = [];
      parser.visit(ast, {
        VariableDeclaration: function (node) {
          watchedFields.forEach((v) => {
            if (
              node.name === v.variableName.trim() &&
              node.isStateVar &&
              node.identifier?.range
            ) {
              // Find the first semicolon after the identifier
              const codeAfterIdentifier = sourceCode.substring(
                node.identifier.range[1]
              );
              const semicolonIndex = codeAfterIdentifier.indexOf(";");
              if (semicolonIndex !== -1) {
                changes.push({
                  start: node.identifier.range[1] + 1,
                  end: node.identifier.range[1] + semicolonIndex,
                  value: " = " + v.value,
                });
              }
            }
          });
        },
      });

      // Sort changes by range in descending order
      changes.sort((a, b) => b.start - a.start);

      // Apply changes from end to start
      let processed = sourceCode;
      changes.forEach(({ start, end, value }) => {
        processed =
          processed.substring(0, start) + value + processed.substring(end);
      });

      setTemplate(processed);
    } catch (e) {
      console.error(e);
      if (e instanceof parser.ParserError) {
        setTemplate("Error parsing the code");
      } else {
        setTemplate("Not a valid solidity code");
      }
    }
  }, [watchedFields, sourceCode, values]);

  return (
    <div className="min-h-screen bg-dashboardBG">
      <div className="gradient-dark-bg flex items-center justify-between bg-blockBg px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>

      {/* Main Content */}
      <div className="min-h-[65vh] overflow-auto p-[45px]">
        <AppPageTitle className="mb-[20px]">
          Dynamic Replacement Demo
        </AppPageTitle>
        <h3 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
          This is a simple demo to explain the logic of Dynamic Replacement
        </h3>
        <h3 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
          These replacements are applied to your Smart Contracts, allowing you
          to change constants without needing to create a new branch
        </h3>

        <FormProvider {...methods}>
          <div>
            <div className="grid grid-cols-1 gap-10 md:grid-cols-[1fr_3fr]">
              {/* Input Fields Section */}
              <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px] ">
                {fields.map((field, index) => (
                  <div
                    key={field.id}
                    className="relative mb-[16px] min-w-[400px] rounded border-b-orange-50 p-4"
                  >
                    <AppInput
                      className="mb-[8px]"
                      label="Variable Name"
                      {...register(`fields.${index}.variableName` as const, {
                        required: "Variable Name is required",
                      })}
                      type="text"
                      defaultValue={field.variableName}
                    />
                    <AppInput
                      className="mb-[8px]"
                      label="Value"
                      {...register(`fields.${index}.value` as const, {
                        required: "Value is required",
                      })}
                      type="text"
                      defaultValue={field.value}
                    />
                    <button
                      type="button"
                      onClick={() => remove(index)}
                      className="absolute bottom-1 right-1 size-[18px] text-white underline"
                      title="Delete Field Group"
                    >
                      Remove
                    </button>
                  </div>
                ))}

                <AppButton
                  type="button"
                  onClick={() => append({ variableName: "", value: "" })}
                  className="mt-4 rounded  p-2 text-white transition-colors "
                >
                  Add More Fields
                </AppButton>
              </div>

              <div className="min-w-[450px] flex-wrap border border-y-0 border-l-0 border-r-divider pr-[40px]">
                <div className="min-w-[400px]">
                  <label className="mb-2 block text-[16px] leading-[19px] text-textPrimary">
                    Source Code
                  </label>
                  <textarea
                    id="sourceCode"
                    value={sourceCode}
                    onChange={(e) => setSourceCode(e.target.value)}
                    className="h-[400px] w-full rounded p-4 font-mono"
                    placeholder="Paste your Solidity code here..."
                  />
                  <label className="mb-2 mt-4 block text-[16px] leading-[19px] text-textPrimary">
                    Generated Template
                  </label>
                  <AppCode code={template} language="solidity" />
                </div>
              </div>
            </div>
          </div>
        </FormProvider>
      </div>
      <Footer />
    </div>
  );
}
