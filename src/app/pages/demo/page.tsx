"use client";

import Link from "next/link";
import { FaGith<PERSON>, FaPlay } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { Recon } from "@/app/components/Svgs";

import styles from "./page.module.scss";

export default function Vision() {
  return (
    <div className="min-h-screen grow overflow-y-auto bg-dashboardBG text-textPrimary">
      <Link href="/">
        <div className={styles.recon}>
          <Recon />
        </div>
      </Link>
      <main className={styles.section}>
        <h1>The Recon Demo</h1>
        <p>Recon scaffolds invariant testing via Medusa and Echidna</p>
        <p>Recon is compatible with any Foundry Project</p>
        <Link href="https://book.getrecon.xyz/" target="_blank">
          <AppButton>
            <p>Written Tutorials</p>
          </AppButton>
        </Link>
        <h2>Demo</h2>
        <p>
          <iframe
            width="560"
            height="315"
            src="https://www.youtube.com/embed/z0sktBuJfEI"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          ></iframe>
        </p>

        <h2>5 Minutes Tutorial</h2>
        <p>
          <iframe
            width="560"
            height="315"
            src="https://www.youtube.com/embed/RwfiPrxbdBg"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
          ></iframe>
        </p>
        <Link href="/dashboard">
          <h2>To use Recon, you need to create an account</h2>
          <AppButton>Create a Free Account!</AppButton>
        </Link>

        <h2>Button Sizes Demo</h2>
        <div className="flex flex-col gap-4 max-w-md">
          <div className="flex items-center gap-4">
            <span className="w-16 text-sm">XS:</span>
            <AppButton size="xs">Extra Small</AppButton>
          </div>
          <div className="flex items-center gap-4">
            <span className="w-16 text-sm">SM:</span>
            <AppButton size="sm">Small</AppButton>
          </div>
          <div className="flex items-center gap-4">
            <span className="w-16 text-sm">Default:</span>
            <AppButton size="default">Default</AppButton>
          </div>
          <div className="flex items-center gap-4">
            <span className="w-16 text-sm">LG:</span>
            <AppButton size="lg">Large</AppButton>
          </div>
        </div>

        <h3>With Variants</h3>
        <div className="flex flex-col gap-4 max-w-md">
          <div className="flex items-center gap-2">
            <AppButton size="xs" variant="primary">
              Primary XS
            </AppButton>
            <AppButton size="sm" variant="secondary">
              Secondary SM
            </AppButton>
            <AppButton size="default" variant="outline">
              Outline Default
            </AppButton>
            <AppButton size="lg" variant="primary">
              Primary LG
            </AppButton>
          </div>
        </div>

        <h3>With Icons</h3>
        <div className="flex flex-col gap-4 max-w-md">
          <div className="flex items-center gap-2">
            <AppButton size="xs" variant="primary" leftIcon={<FaGithub />}>
              XS with Icon
            </AppButton>
            <AppButton size="sm" variant="secondary" rightIcon={<FaPlay />}>
              SM with Icon
            </AppButton>
            <AppButton size="default" variant="outline" leftIcon={<FaGithub />}>
              Default with Icon
            </AppButton>
            <AppButton size="lg" variant="primary" rightIcon={<FaPlay />}>
              LG with Icon
            </AppButton>
          </div>
        </div>
      </main>
    </div>
  );
}
