import Link from "next/link";

import { Recon } from "@/app/components/Svgs";

import styles from "./page.module.scss";

export default async function EcosystemFuzzing() {
  return (
    <div className="min-h-screen grow overflow-y-auto bg-dashboardBG text-textPrimary">
      <Link href="/">
        <div className={styles.recon}>
          <Recon />
        </div>
      </Link>
      <main className={styles.section}>
        <h1>Ecosystem Fuzzing as a Service</h1>
        <p>
          The Recon team can setup automated fuzzing for your entire chain /
          ecosystem
        </p>
        <p>
          Give your developers the peace of mind of having automated testing,
          with our continous fuzzing services
        </p>
        <h2>How does it work?</h2>
        <p>Get in touch</p>
        <p>We'll write automated handlers and properties for your project</p>
        <p>
          We'll provision invites and keys for all your ecosystem developers
        </p>
        <p>All your developers are now safer by default!</p>
      </main>
    </div>
  );
}
