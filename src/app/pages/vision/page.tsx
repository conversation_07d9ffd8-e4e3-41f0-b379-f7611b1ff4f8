"use client";

import Link from "next/link";

import { Recon } from "@/app/components/Svgs";
import { useGetDiscord } from "@/app/services/discord.hook.ts";

import styles from "./page.module.scss";

export default function Vision() {
  const { data: discordUrl } = useGetDiscord();
  return (
    <div className="min-h-screen grow overflow-y-auto bg-dashboardBG text-textPrimary">
      <Link href="/">
        <div className={styles.recon}>
          <Recon />
        </div>
      </Link>
      <main className={styles.section}>
        <h1>Most exploits can be prevented</h1>
        <p>
          We believe that mixing manual and automated research is the way to
          prevent more exploit
        </p>
        <p>Recon exists to help you find more bugs, before hackers do</p>
        <p>Invariant Testing helps you try random combinations</p>
        <p>Helping you identify and test against edge cases</p>
        <h2>Fuzzing everything</h2>
        <p>
          The long term goal for Recon is to find any exploit that could be
          found automatically, with close to no knowledge about the smart
          contracts
        </p>
        {discordUrl && (
          <p>
            If you believe that we can make the chain safer,{" "}
            <a href={discordUrl} target="_blank" rel="nofollow noreferrer">
              join us
            </a>
          </p>
        )}
      </main>
    </div>
  );
}
