"use client";
import { ENV_TYPE } from "@/app/app.constants";
import ToolPageLayout from "@/app/components/ToolPageLayout";

export default function HalmosParserInternal() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.HALMOS}
      toolName="Halmos Logs Scraper"
      toolDescription={[
        "This tool allows to scrape halmos logs for broken properties repros",
        "Paste your raw halmos logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/WDdFVZpTAZo"
      youtubeOverlayText="Learn how to use Halmos"
    />
  );
}
