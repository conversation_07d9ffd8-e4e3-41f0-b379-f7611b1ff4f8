"use client";
import Link from "next/link";
import { useCallback, useMemo, useState } from "react";

import { AppInput } from "@/app/components/app-input";
import { AppLogo } from "@/app/components/app-logo";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppCheckbox } from "@/app/components/app-checkbox";
import Footer from "@/app/components/Footer/Footer";

import {
  doKeccak256,
  getAllRolesEvents,
  getRoleMembersFromEnumerable,
  inferRolesFromSource,
} from "oz-roles-scraper";
import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppSpinner } from "@/app/components/app-spinner";

export default function RolesPageInternal() {
  const [rpcUrl, setRpcUrl] = useState("https://eth.llamarpc.com");
  const [contractAddress, setContractAddress] = useState(
    "******************************************"
  );
  const [isEnumerable, setIsEnumerable] = useState(false);
  const [role, setRole] = useState("");
  const [roleHash, setRoleHash] = useState("");

  const [foundRoles, setFoundRoles] = useState<string[]>([]);
  const [roleMembers, setRoleMembers] = useState<string[]>([]);
  const [roleId, setRoleId] = useState<string>("");

  const [startBlock, setStartBlock] = useState<number>(18290587);
  const [isLoadingBatchEvents, setIsLoadingBatchEvents] =
    useState<boolean>(false);
  const [allRoleEvents, setAllRoleEvents] = useState<any>([]);

  const inferRoles = useCallback(async () => {
    if (!rpcUrl || !contractAddress) return [];
    const roles = await inferRolesFromSource(rpcUrl, contractAddress);

    setFoundRoles(roles.map((role) => role.name));
  }, [rpcUrl, contractAddress]);

  const fetchRoleMembers = useCallback(async () => {
    if (!rpcUrl || !contractAddress || !roleHash) return [];
    const result = await getRoleMembersFromEnumerable(
      rpcUrl,
      contractAddress,
      roleHash
    );
    setRoleMembers(result.members.map((member) => member));
  }, [rpcUrl, contractAddress, roleHash]);

  const generateRoleId = useCallback(async () => {
    if (!role) return;
    const result = await doKeccak256(role);
    setRoleId(result);
  }, [role]);

  const fetchRoleEvents = useCallback(async () => {
    if (!rpcUrl || !contractAddress) return [];
    setIsLoadingBatchEvents(true);
    const result = await getAllRolesEvents(
      rpcUrl,
      contractAddress,
      role,
      startBlock
    );
    setAllRoleEvents(result);
    setIsLoadingBatchEvents(false);
  }, [role, rpcUrl, contractAddress, startBlock]);

  return (
    <div className="bg-dashboardBG min-h-screen">
      <div className="gradient-dark-bg bg-blockBg flex items-center justify-between px-[40px] py-[20px]">
        <Link href="/dashboard" className="cursor-pointer">
          <AppLogo />
        </Link>
      </div>
      <div className="p-[45px]">
        <div className="mb-[20px]">
          <AppPageTitle className="mb-[3px]">
            OpenZeppelin Roles Scraper
          </AppPageTitle>

          <p className="text-textSecondary mb-[3px] block text-[15px] leading-[18px]">
            This tool helps you analyze and extract role-based access control
            information from smart contracts
          </p>
          <p className="text-textSecondary mb-[3px] block text-[15px] leading-[18px]">
            Infer roles from contract ABI, generate role IDs, fetch role members
            for enumerable roles
          </p>
        </div>
        <AppInput
          className="mb-[8px]"
          label="RPC URL"
          value={rpcUrl}
          onChange={(e) => setRpcUrl(e.target.value)}
          type="text"
        />

        <AppInput
          className="mb-[8px]"
          label="Contract Address"
          value={contractAddress}
          onChange={(e) => setContractAddress(e.target.value)}
          type="text"
        />

        <div className="mb-[32px]">
          <AppCheckbox
            label="Is Enumerable"
            checked={isEnumerable}
            onChange={(e) => setIsEnumerable(e.target.checked)}
          />

          {isEnumerable && (
            <div className="mb-[32px]">
              <h2 className="text-xl font-semibold text-textPrimary mb-[16px]">
                Get Role Members (Enumerable)
              </h2>
              <AppInput
                className="mb-[8px]"
                label="Role Hash"
                value={roleHash}
                onChange={(e) => setRoleHash(e.target.value)}
              />
              <AppButton onClick={fetchRoleMembers} className="mb-[16px]">
                Get Role Members
              </AppButton>

              {roleMembers.length > 0 && (
                <div className="rounded-md bg-blockBg p-4">
                  <h3 className="text-lg font-medium text-textPrimary mb-[8px]">
                    Role Members:
                  </h3>
                  {roleMembers.map((member) => (
                    <div
                      key={member}
                      className="text-textSecondary font-mono mb-[4px]"
                    >
                      {member}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="mb-[32px]">
          <h2 className="text-xl font-semibold text-textPrimary mb-[16px]">
            Scrape Roles from ABI
          </h2>
          <AppButton onClick={inferRoles} className="mb-[16px]">
            Infer Roles
          </AppButton>

          {foundRoles.length > 0 && (
            <div className="rounded-md bg-blockBg p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-[8px]">
                Found Roles:
              </h3>
              {foundRoles.map((role) => (
                <div key={role} className="text-textSecondary mb-[4px]">
                  {role}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mb-[32px]">
          <h2 className="text-xl font-semibold text-textPrimary mb-[16px]">
            Generate Role ID from Role Name
          </h2>
          <AppInput
            className="mb-[8px]"
            label="Role Name"
            value={role}
            onChange={(e) => setRole(e.target.value)}
          />
          <AppButton onClick={generateRoleId} className="mb-[16px]">
            Generate Role ID
          </AppButton>

          {roleId && (
            <div className="rounded-md bg-blockBg p-4">
              <h3 className="text-lg font-medium text-textPrimary mb-[8px]">
                Role ID:
              </h3>
              <p className="text-textSecondary font-mono break-all">{roleId}</p>
            </div>
          )}
        </div>
      </div>
      <Footer />
    </div>
  );
}
