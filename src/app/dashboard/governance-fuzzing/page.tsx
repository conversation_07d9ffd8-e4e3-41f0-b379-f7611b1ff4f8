"use client";

import { AppInput } from "@/app/components/app-input";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { AppPageTitle } from "@/app/components/app-page-title";
import { AppButton } from "@/app/components/app-button";
import { useGetRecipes } from "@/app/services/recipes.hook";
import { FormProvider, useForm, useFieldArray } from "react-hook-form";
import { ethers } from "ethers";
import axios from "axios";
import { useState, useEffect } from "react";
import { useGetGovernanceFuzzing } from "@/app/services/governanceFuzzing.hook";
import { AppCode } from "@/app/components/app-code";
import { Toaster, toast } from "react-hot-toast";
import type { GovernanceFuzzing } from "@/app/services/governanceFuzzing.hook";
import { chains } from "@/lib/utils";
export type GitHubLinkFormValues = {
  contractAddress: string;
  chainId: number;
  recipeId: string;
  eventName: string;
  parameters: Array<{
    type: string;
    isIndexed: boolean;
    replacement?: string;
    unused: boolean;
  }>;
};

// ADD check for for fork replay ? (vm warp and roll )

export default function GovernanceFuzzing() {
  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      contractAddress: "",
      chainId: 1,
      recipeId: "",
      eventName: "",
      parameters: [],
    },
  });

  const { register, setValue, handleSubmit, watch, formState } = methods;
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "parameters",
  });
  const { data: recipes, isLoading } = useGetRecipes();
  const { data: governanceFuzzing } = useGetGovernanceFuzzing();
  const [showEventDefinition, setShowEventDefinition] = useState(false);
  const [topic, setTopic] = useState<string>("");
  const [eventDefinition, setEventDefinition] = useState<string>("");
  const [isEditing, setIsEditing] = useState<Map<string, boolean>>(new Map());
  const eventName = watch("eventName");
  const parameters = watch("parameters");
  const [editForm, setEditForm] = useState<{
    address: string;
    chainId: string;
    eventDefinition: string;
    topic: string;
    prepareContracts: Array<{ target: string; replacement: string }>;
    id: string;
  } | null>(null);

  useEffect(() => {
    if (eventName && parameters?.length > 0) {
      const eventDefConstructedForTopic = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()}`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setTopic(
        ethers.keccak256(ethers.toUtf8Bytes(eventDefConstructedForTopic))
      );
      const eventDefConstructed = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()} indexed`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setEventDefinition(eventDefConstructed);
    }
  }, [eventName, parameters, formState]);

  const onSubmit = async (data: GitHubLinkFormValues) => {
    // Construct event definition
    const eventDefConstructedForTopic = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()}`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;

    const formattedEventToTopics = ethers.keccak256(
      ethers.toUtf8Bytes(eventDefConstructedForTopic)
    );

    const eventDefConstructed = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()} indexed`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;
    if (
      !data.parameters.some((param) => param.replacement?.includes("XX")) &&
      data.parameters.some((param) => param.unused === false)
    ) {
      toast.error("No XX found in replacement");
      return;
    }
    const prepContract = data.parameters.map((param, index) => {
      // Split the string by XX and join all but the last part

      const parts = param.replacement.split("XX");
      let lastPart = parts.pop(); // Remove and get the last part
      if (lastPart.endsWith(";")) {
        lastPart = lastPart.slice(0, -1);
      }
      const newReplacement = parts.join("XX") + `$_${index + 1}` + lastPart;

      return {
        target: `${param.replacement}`,
        replacement: `${newReplacement};`,
        endOfTargetMarker: "[^;]*",
        targetContract: "Setup.sol",
        unused: param.unused,
      };
    });

    console.log({
      contractAddress: data.contractAddress,
      recipeId: data.recipeId,
      topic: formattedEventToTopics,
      eventDefinition: eventDefConstructed,
      prepContract,
      chainId: data.chainId,
    });

    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing`,
      data: {
        contractAddress: data.contractAddress,
        recipeId: data.recipeId,
        topic: formattedEventToTopics,
        eventDefinition: eventDefConstructed,
        prepContract: prepContract,
        chainId: data.chainId,
      },
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing setup");
      // window.location.reload();
    } else {
      toast.error("Fail to create Governance Fuzzing");
    }
  };

  const handleDelete = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/delete`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      // reload the page
      window.location.reload();
    }
  };

  const handleToggle = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/toggle`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      window.location.reload();
    }
  };
  const contractAddress = watch("contractAddress");
  const chainId = watch("chainId");
  const recipeId = watch("recipeId");

  const handleEdit = async (id: string) => {
    setIsEditing(new Map(isEditing).set(id, true));
    const govFuzz = governanceFuzzing.find((g) => g.id === id);
    console.log(govFuzz);
    if (govFuzz) {
      setEditForm({
        address: govFuzz.address,
        chainId: govFuzz.chainId.toString(),
        eventDefinition: govFuzz.eventDefinition,
        topic: govFuzz.topic,
        prepareContracts:
          govFuzz.recipes[0]?.fuzzerArgs?.prepareContracts || [],
        id: govFuzz.id,
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setEditForm((prev) => {
      if (!prev) return null;
      let newTopic = prev.topic;
      if (field === "eventDefinition") {
        newTopic = ethers.keccak256(ethers.toUtf8Bytes(value));
      }
      return {
        ...prev,
        [field]: value,
        topic: newTopic,
      };
    });
  };

  const handlePrepareContractChange = (
    index: number,
    field: string,
    value: string
  ) => {
    setEditForm((prev) => {
      if (!prev) return null;
      const newPrepareContracts = [...prev.prepareContracts];
      newPrepareContracts[index] = {
        ...newPrepareContracts[index],
        [field]: value,
      };

      return {
        ...prev,
        prepareContracts: newPrepareContracts,
      };
    });
  };

  const editValidate = async () => {
    console.log("editForm", editForm);
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/put`,
      data: editForm,
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing updated");
      window.location.reload();
    } else {
      toast.error("Fail to update Governance Fuzzing");
    }
  };

  return (
    <div className="min-h-screen bg-dashboardBG">
      <Toaster position="top-center" reverseOrder={false} />

      <div className="min-h-[65vh] overflow-auto p-[45px]">
        <div className="mb-[20px] mt-[40px] text-[15px] leading-[18px] text-textSecondary">
          <AppPageTitle>Governance Fuzzing</AppPageTitle>
          <p>Setup Event Listeners tied to your contract</p>
          <p>Please talk to staff to set these up if you need help</p>
        </div>
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-10 md:grid-cols-[1fr_3fr]">
              <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
                {/* Basic Information */}
                <AppInput
                  className="mb-[8px]"
                  label="Contract Address"
                  {...register("contractAddress", { required: true })}
                  type="text"
                />
                <AppInputDropdown
                  className="mb-[8px]"
                  label="Chain"
                  {...register("chainId")}
                  type="text"
                  dropdownItems={chains.map((chain) => ({
                    id: chain.id.toString(),
                    label: `${chain.id} - ${chain.name}`,
                    fields: chain,
                  }))}
                  onItemSelect={(id) => setValue("chainId", Number(id))}
                />
                {recipes && recipes.length > 0 && !isLoading ? (
                  <AppInputDropdown
                    className="mb-[8px] w-full"
                    {...register("recipeId", { required: true })}
                    type="text"
                    label="Recipe"
                    placeholder="Search by Name, Id, repo ..."
                    dropdownItems={recipes.map((rec) => ({
                      id: rec.id,
                      label: `${rec.displayName}`,
                      fields: rec,
                    }))}
                    onItemSelect={(id) => setValue("recipeId", id as string)}
                  />
                ) : isLoading ? (
                  <p className="text-white">Loading recipes ...</p>
                ) : (
                  <p className="text-white">No recipes found</p>
                )}

                {/* Event Definition Section */}
                {contractAddress && chainId && recipeId && (
                  <div className="mt-6">
                    {!showEventDefinition ? (
                      <AppButton
                        type="button"
                        onClick={() => setShowEventDefinition(true)}
                        className="mb-4"
                      >
                        Define New Event
                      </AppButton>
                    ) : (
                      <>
                        <AppInput
                          className="mb-[8px]"
                          label="Event Name"
                          placeholder="e.g. AddNumber"
                          {...register("eventName", { required: true })}
                          type="text"
                        />

                        <h2 className="mb-[16px] text-[16px] leading-[19px] text-textPrimary">
                          Event Parameters ( MUST match event definition )
                        </h2>

                        {fields.map((field, index) => (
                          <div
                            key={field.id}
                            className="relative mb-[16px] rounded p-4"
                          >
                            <div className="mb-4 flex items-center gap-4">
                              <AppInput
                                className="flex-1"
                                label="Parameter Type"
                                placeholder="e.g. address, uint256"
                                {...register(`parameters.${index}.type`, {
                                  required: true,
                                })}
                                type="text"
                              />
                              <label className="flex items-center gap-2 text-textSecondary">
                                <input
                                  type="checkbox"
                                  {...register(`parameters.${index}.isIndexed`)}
                                  className="size-4"
                                />
                                Indexed
                              </label>
                            </div>

                            <div className="flex flex-col">
                              <AppInput
                                label="Replacement"
                                {...register(`parameters.${index}.replacement`)}
                                type="text"
                                placeholder='bytes constant PAYLOAD = bytes(hex"XX")'
                              />
                              <label className="flex items-center gap-2 text-textSecondary">
                                <input
                                  type="checkbox"
                                  {...register(`parameters.${index}.unused`)}
                                  className="size-4"
                                />
                                Unused
                              </label>
                              <p className="text-textSecondary">
                                Ex: <pre>uint8 DECIMALS = uint8(XX)</pre>
                                Define the replacement in your contract. Not
                                that you need to input XX to allow us to replace
                                XX with the value from the event
                              </p>
                            </div>

                            <button
                              type="button"
                              onClick={() => remove(index)}
                              className="absolute right-2 top-2 text-red-500"
                            >
                              Remove
                            </button>
                          </div>
                        ))}

                        <AppButton
                          type="button"
                          onClick={() =>
                            append({
                              type: "",
                              isIndexed: false,
                              replacement: "",
                              unused: false,
                            })
                          }
                          className="mt-4"
                        >
                          Add Parameter
                        </AppButton>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            {topic && eventDefinition ? (
              <div>
                <p className="text-white">Topic: {topic}</p>
                <p className="text-white">
                  Event Definition: {eventDefinition}
                </p>
                <p className="text-white">
                  Make sure you verify this topic is correct before submitting
                </p>
                <p className="text-white">
                  Incorrect topic will make us miss events on chain
                </p>
              </div>
            ) : (
              <p className="text-white">No topic</p>
            )}

            {showEventDefinition && (
              <AppButton
                type="submit"
                className="mt-4 rounded p-2 text-white transition-colors"
              >
                Submit
              </AppButton>
            )}
          </form>
        </FormProvider>
      </div>

      {governanceFuzzing?.length > 0 && (
        <div className="flex flex-col gap-[20px] p-[45px]">
          <h2 className="mb-[28px] text-[22px] leading-[26px] text-textPrimary text-white">
            Existing Governance Fuzzing
          </h2>
          {governanceFuzzing.map((govFuzz) => (
            <>
              {isEditing.get(govFuzz.id) && editForm && (
                <div className="flex flex-col gap-[20px] rounded-lg border border-white p-[20px]">
                  <label className="text-white" htmlFor="address">
                    Address
                  </label>
                  <input
                    id="address"
                    type="text"
                    value={editForm.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                  />
                  <label className="text-white" htmlFor="chainId">
                    Chain Id
                  </label>
                  <input
                    id="chainId"
                    type="text"
                    value={editForm.chainId}
                    onChange={(e) =>
                      handleInputChange("chainId", e.target.value)
                    }
                  />
                  <label className="text-white" htmlFor="prepareContracts">
                    Prepare Contracts
                  </label>
                  {editForm.prepareContracts.map((contract, index) => (
                    <input
                      key={index}
                      type="text"
                      value={contract.target}
                      onChange={(e) =>
                        handlePrepareContractChange(
                          index,
                          "target",
                          e.target.value
                        )
                      }
                    />
                  ))}
                  <label className="text-white" htmlFor="eventDefinition">
                    Event Definition
                  </label>
                  <input
                    id="eventDefinition"
                    type="text"
                    value={editForm.eventDefinition}
                    onChange={(e) =>
                      handleInputChange("eventDefinition", e.target.value)
                    }
                  />
                  <label className="text-white" htmlFor="topic">
                    Topic
                  </label>
                  <input
                    id="topic"
                    type="text"
                    value={editForm.topic}
                    onChange={(e) =>
                      handleInputChange(
                        "topic",
                        ethers.keccak256(
                          ethers.toUtf8Bytes(editForm.eventDefinition)
                        )
                      )
                    }
                    disabled
                  />
                  <div className="flex gap-4">
                    <AppButton
                      type="button"
                      onClick={editValidate}
                      className="mt-4"
                    >
                      Validate edit
                    </AppButton>

                    <AppButton
                      type="button"
                      onClick={() =>
                        setIsEditing(new Map(isEditing).set(govFuzz.id, false))
                      }
                      className="mt-4"
                    >
                      Cancel edit
                    </AppButton>
                  </div>
                </div>
              )}
              {!isEditing.get(govFuzz.id) && (
                <div className="text-[18px] leading-[21px] text-textPrimary">
                  <div className="my-3 flex justify-between"></div>
                  <AppCode
                    code={JSON.stringify(govFuzz, null, 2)}
                    language="json"
                  />
                  <div className="flex w-full justify-between">
                    <AppButton
                      type="button"
                      onClick={() => handleDelete(govFuzz.id)}
                    >
                      Delete
                    </AppButton>
                    <AppButton
                      type="button"
                      onClick={() => handleToggle(govFuzz.id)}
                    >
                      Toggle Gov. Fuzzing
                    </AppButton>
                    <AppButton
                      type="button"
                      onClick={() => handleEdit(govFuzz.id)}
                    >
                      Edit
                    </AppButton>
                  </div>
                </div>
              )}
            </>
          ))}
        </div>
      )}
    </div>
  );
}
